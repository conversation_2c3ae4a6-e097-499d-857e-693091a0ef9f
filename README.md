# WhatsApp Gemini Bot

Bot WhatsApp yang didukung oleh Google Gemini AI, dibangun dengan TypeScript dan whatsapp-web.js.

## 🚀 Fitur

- ✅ Integrasi dengan Google Gemini AI (gemini-2.5-flash)
- ✅ Dukungan chat pribadi dan grup
- ✅ Konfigurasi prefix untuk grup
- ✅ Streaming response dari AI
- ✅ Session management untuk WhatsApp Web
- ✅ Error handling yang robust
- ✅ Logging yang informatif
- ✅ TypeScript untuk type safety

## 📋 Prasyarat

- Node.js (versi 16 atau lebih baru)
- npm atau yarn
- Google Gemini API Key
- WhatsApp account

## 🛠️ Instalasi

1. **Clone atau download project ini**

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Setup environment variables:**
   ```bash
   cp .env.example .env
   ```

4. **Edit file `.env` dan isi dengan konfigurasi Anda:**
   ```env
   GEMINI_API_KEY=your_gemini_api_key_here
   BOT_NAME=WhatsApp Gemini Bot
   BOT_PREFIX=!
   SESSION_NAME=whatsapp-session
   ```

## 🔑 Mendapatkan Gemini API Key

1. Kunjungi [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Login dengan akun Google Anda
3. Buat API key baru
4. Copy API key dan masukkan ke file `.env`

## 🚀 Menjalankan Bot

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
# Build project
npm run build

# Start bot
npm start
```

## 📱 Cara Menggunakan

1. **Jalankan bot** dengan perintah di atas
2. **Scan QR code** yang muncul di terminal dengan WhatsApp di ponsel Anda
3. **Bot siap digunakan!**

### Chat Pribadi
- Kirim pesan apa saja ke bot
- Bot akan merespons menggunakan Gemini AI

### Chat Grup
- Tambahkan bot ke grup
- Gunakan prefix (default: `!`) sebelum pesan
- Contoh: `!Halo, apa kabar?`

## ⚙️ Konfigurasi

| Variable | Deskripsi | Default |
|----------|-----------|---------|
| `GEMINI_API_KEY` | API key untuk Google Gemini | **Required** |
| `BOT_NAME` | Nama bot | `WhatsApp Gemini Bot` |
| `BOT_PREFIX` | Prefix untuk perintah di grup | `!` |
| `SESSION_NAME` | Nama session WhatsApp | `whatsapp-session` |

## 📝 Scripts NPM

- `npm run dev` - Jalankan dalam mode development dengan hot reload
- `npm run build` - Build project ke JavaScript
- `npm start` - Jalankan bot dari build hasil
- `npm run clean` - Hapus folder build

## 🔧 Troubleshooting

### Bot tidak merespons
- Pastikan API key Gemini valid
- Cek koneksi internet
- Lihat log error di terminal

### QR code tidak muncul
- Pastikan tidak ada session WhatsApp lain yang aktif
- Hapus folder `.wwebjs_auth` dan coba lagi

### Error saat install dependencies
- Pastikan Node.js versi 16+
- Coba hapus `node_modules` dan `package-lock.json`, lalu install ulang

## 📁 Struktur Project

```
src/
├── ai/
│   └── gemini.ts          # Service untuk Gemini AI
├── config/
│   └── index.ts           # Konfigurasi dan environment
├── handlers/
│   └── messageHandler.ts  # Handler untuk pesan WhatsApp
├── types/
│   └── index.ts           # TypeScript interfaces
├── whatsapp/
│   └── client.ts          # WhatsApp client wrapper
└── index.ts               # Entry point aplikasi
```

## 🤝 Kontribusi

Kontribusi sangat diterima! Silakan buat issue atau pull request.

## 📄 Lisensi

ISC License

## ⚠️ Disclaimer

Bot ini menggunakan WhatsApp Web melalui puppeteer. Pastikan untuk mematuhi Terms of Service WhatsApp dan gunakan dengan bijak.
