import express, { Request, Response, Application } from 'express';
import cors from 'cors';
import { QRCodeService } from '../services/qrCodeService';

export class ExpressServer {
  private app: Application;
  private server: any;
  private port: number;
  private qrCodeService: QRCodeService;

  constructor(port: number = 3000) {
    this.port = port;
    this.app = express();
    this.qrCodeService = QRCodeService.getInstance();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    // Enable CORS for all routes
    this.app.use(cors());

    // Parse JSON bodies
    this.app.use(express.json());

    // Parse URL-encoded bodies
    this.app.use(express.urlencoded({ extended: true }));

    // Add request logging
    this.app.use((req: Request, _res: Response, next) => {
      console.log(`📡 ${req.method} ${req.path} - ${new Date().toISOString()}`);
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (_req: Request, res: Response): void => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      });
    });

    // Get QR code as image
    this.app.get('/qr-code', async (_req: Request, res: Response): Promise<void> => {
      try {
        const qrBuffer = await this.qrCodeService.getQRCodeBuffer();

        if (!qrBuffer) {
          res.status(404).json({
            error: 'QR code not available',
            message: 'No active QR code found. Please ensure WhatsApp is initializing.'
          });
          return;
        }

        res.setHeader('Content-Type', 'image/png');
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.send(qrBuffer);
      } catch (error) {
        console.error('❌ Error serving QR code:', error);
        res.status(500).json({
          error: 'Internal server error',
          message: 'Failed to generate QR code image'
        });
      }
    });

    // Get QR code as data URL (JSON response)
    this.app.get('/qr-code/data', (_req: Request, res: Response): void => {
      try {
        const qrData = this.qrCodeService.getCurrentQRCode();

        if (!qrData || !this.qrCodeService.isQRCodeAvailable()) {
          res.status(404).json({
            error: 'QR code not available',
            message: 'No active QR code found or QR code has expired.'
          });
          return;
        }

        res.json({
          dataURL: qrData.dataURL,
          timestamp: qrData.timestamp,
          isActive: qrData.isActive
        });
      } catch (error) {
        console.error('❌ Error serving QR code data:', error);
        res.status(500).json({
          error: 'Internal server error',
          message: 'Failed to get QR code data'
        });
      }
    });

    // Get WhatsApp connection status
    this.app.get('/status', (_req: Request, res: Response): void => {
      try {
        const status = this.qrCodeService.getWhatsAppStatus();
        const qrAvailable = this.qrCodeService.isQRCodeAvailable();

        res.json({
          ...status,
          qrCodeAvailable: qrAvailable,
          serverTime: new Date().toISOString()
        });
      } catch (error) {
        console.error('❌ Error getting status:', error);
        res.status(500).json({
          error: 'Internal server error',
          message: 'Failed to get WhatsApp status'
        });
      }
    });

    // Serve the main web interface
    this.app.get('/', (_req: Request, res: Response): void => {
      res.send(this.getWebInterface());
    });

    // API documentation endpoint
    this.app.get('/api', (_req: Request, res: Response): void => {
      res.json({
        title: 'WhatsApp QR Code API',
        version: '1.0.0',
        endpoints: {
          'GET /': 'Web interface for QR code display',
          'GET /health': 'Health check endpoint',
          'GET /qr-code': 'Get QR code as PNG image',
          'GET /qr-code/data': 'Get QR code as JSON data URL',
          'GET /status': 'Get WhatsApp connection status',
          'GET /api': 'This API documentation'
        },
        usage: {
          qrCode: 'Access /qr-code to get the current QR code image',
          webInterface: 'Access / to view the web interface',
          status: 'Access /status to check connection status'
        }
      });
    });

    // 404 handler
    this.app.use('*', (req: Request, res: Response) => {
      res.status(404).json({
        error: 'Not found',
        message: `Route ${req.originalUrl} not found`,
        availableRoutes: ['/', '/qr-code', '/qr-code/data', '/status', '/health', '/api']
      });
    });
  }

  private getWebInterface(): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp QR Code Scanner</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
        }
        .qr-container {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        .qr-code {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .status.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.ready {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .refresh-btn {
            background: #25D366;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background 0.3s;
        }
        .refresh-btn:hover {
            background: #128C7E;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #e7f3ff;
            border-radius: 8px;
            color: #0c5460;
            font-size: 14px;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #25D366;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 WhatsApp QR Scanner</h1>
        <p class="subtitle">Scan the QR code below with your WhatsApp mobile app</p>
        
        <div id="status" class="status loading">
            <div class="loading-spinner"></div>
            Checking connection status...
        </div>
        
        <div class="qr-container" id="qrContainer">
            <div class="loading-spinner"></div>
            <p>Loading QR code...</p>
        </div>
        
        <button class="refresh-btn" onclick="refreshQRCode()">🔄 Refresh QR Code</button>
        <button class="refresh-btn" onclick="checkStatus()">📱 Check Status</button>
        
        <div class="info">
            <strong>Instructions:</strong><br>
            1. Open WhatsApp on your phone<br>
            2. Go to Settings → Linked Devices<br>
            3. Tap "Link a Device"<br>
            4. Scan the QR code above
        </div>
    </div>

    <script>
        let refreshInterval;
        
        async function checkStatus() {
            try {
                const response = await fetch('/status');
                const status = await response.json();
                
                const statusElement = document.getElementById('status');
                
                if (status.isAuthenticated && status.isConnected) {
                    statusElement.className = 'status ready';
                    statusElement.innerHTML = '✅ WhatsApp Connected Successfully!';
                    clearInterval(refreshInterval);
                } else if (status.hasQRCode && status.qrCodeAvailable) {
                    statusElement.className = 'status loading';
                    statusElement.innerHTML = '📱 Waiting for QR code scan...';
                } else {
                    statusElement.className = 'status loading';
                    statusElement.innerHTML = '🔄 Initializing WhatsApp connection...';
                }
                
                return status;
            } catch (error) {
                const statusElement = document.getElementById('status');
                statusElement.className = 'status error';
                statusElement.innerHTML = '❌ Error checking status';
                console.error('Status check failed:', error);
                return null;
            }
        }
        
        async function refreshQRCode() {
            const qrContainer = document.getElementById('qrContainer');
            
            try {
                // Show loading state
                qrContainer.innerHTML = '<div class="loading-spinner"></div><p>Loading QR code...</p>';
                
                const response = await fetch('/qr-code');
                
                if (response.ok) {
                    const blob = await response.blob();
                    const imageUrl = URL.createObjectURL(blob);
                    
                    qrContainer.innerHTML = '<img src="' + imageUrl + '" alt="WhatsApp QR Code" class="qr-code">';
                } else {
                    const errorData = await response.json();
                    qrContainer.innerHTML = '<p style="color: #721c24;">❌ ' + (errorData.message || 'QR code not available') + '</p>';
                }
            } catch (error) {
                qrContainer.innerHTML = '<p style="color: #721c24;">❌ Failed to load QR code</p>';
                console.error('QR code refresh failed:', error);
            }
        }
        
        // Auto-refresh functionality
        function startAutoRefresh() {
            refreshInterval = setInterval(async () => {
                const status = await checkStatus();
                if (status && !status.isAuthenticated && status.qrCodeAvailable) {
                    refreshQRCode();
                }
            }, 5000); // Check every 5 seconds
        }
        
        // Initialize on page load
        window.onload = function() {
            checkStatus();
            refreshQRCode();
            startAutoRefresh();
        };
    </script>
</body>
</html>`;
  }

  public start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(this.port, () => {
          console.log(`🌐 Express server started on port ${this.port}`);
          console.log(`📱 QR Code Web Interface: http://localhost:${this.port}`);
          console.log(`🔗 QR Code API: http://localhost:${this.port}/qr-code`);
          console.log(`📊 Status API: http://localhost:${this.port}/status`);
          resolve();
        });

        this.server.on('error', (error: any) => {
          console.error('❌ Express server error:', error);
          reject(error);
        });
      } catch (error) {
        console.error('❌ Failed to start Express server:', error);
        reject(error);
      }
    });
  }

  public stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('🛑 Express server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  public getPort(): number {
    return this.port;
  }

  public getApp(): Application {
    return this.app;
  }
}
