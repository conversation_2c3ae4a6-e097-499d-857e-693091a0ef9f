import { WhatsAppClient } from './whatsapp/client';
import { GeminiAIService } from './ai/gemini';
import { MessageHandler } from './handlers/messageHandler';
import { getConfig, printConfig } from './config';
import { ExpressServer } from './server/expressServer';

class WhatsAppGeminiBot {
  private whatsappClient: WhatsAppClient;
  private aiService: GeminiAIService;
  private messageHandler: MessageHandler;
  private expressServer: ExpressServer | null = null;
  private config: any;

  constructor() {
    this.config = getConfig();

    this.whatsappClient = new WhatsAppClient(this.config);
    this.aiService = new GeminiAIService(this.config.geminiApiKey);
    this.messageHandler = new MessageHandler(this.whatsappClient, this.aiService, this.config);

    // Initialize Express server if web interface is enabled
    if (this.config.enableWebInterface) {
      this.expressServer = new ExpressServer(this.config.serverPort);
    }
  }

  public async start(): Promise<void> {
    try {
      console.log('🚀 Starting WhatsApp Gemini Bot...\n');

      // Print configuration
      printConfig();
      console.log('');

      // Start Express server if enabled
      if (this.expressServer) {
        console.log('🌐 Starting web interface server...');
        await this.expressServer.start();
        console.log('');
      }

      // Test AI connection
      console.log('🔍 Testing Gemini AI connection...');
      const aiConnected = await this.aiService.testConnection();

      if (!aiConnected) {
        console.error('❌ Failed to connect to Gemini AI. Please check your API key.');
        process.exit(1);
      }

      console.log('✅ Gemini AI connection successful\n');

      // Setup message handler
      this.whatsappClient.onMessage(async (context) => {
        await this.messageHandler.handleMessage(context);
      });

      // Initialize WhatsApp client
      await this.whatsappClient.initialize();

      // Setup graceful shutdown
      this.setupGracefulShutdown();

    } catch (error) {
      console.error('❌ Failed to start bot:', error);
      process.exit(1);
    }
  }

  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      console.log(`\n🛑 Received ${signal}. Shutting down gracefully...`);

      try {
        // Stop Express server first
        if (this.expressServer) {
          await this.expressServer.stop();
        }

        // Then destroy WhatsApp client
        await this.whatsappClient.destroy();
        console.log('✅ Bot shutdown complete');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('uncaughtException', (error) => {
      console.error('❌ Uncaught Exception:', error);
      shutdown('uncaughtException');
    });
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection');
    });
  }
}

// Start the bot
const bot = new WhatsAppGeminiBot();
bot.start().catch((error) => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
