import { WhatsAppClient } from '../whatsapp/client';
import { GeminiAIService } from '../ai/gemini';
import { BotConfig, MessageContext } from '../types';
export declare class MessageHandler {
    private whatsappClient;
    private aiService;
    private config;
    constructor(whatsappClient: WhatsAppClient, aiService: GeminiAIService, config: BotConfig);
    handleMessage(context: MessageContext): Promise<void>;
    private shouldProcessMessage;
    private logMessage;
    getProcessedMessage(context: MessageContext): string;
}
//# sourceMappingURL=messageHandler.d.ts.map