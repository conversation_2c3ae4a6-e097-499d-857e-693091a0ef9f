import { WhatsAppClient } from '../whatsapp/client';
import { GeminiAIService } from '../ai/gemini';
import { BotConfig, MessageContext } from '../types';

export class MessageHandler {
  private whatsappClient: WhatsAppClient;
  private aiService: GeminiAIService;
  private config: BotConfig;

  constructor(whatsappClient: WhatsAppClient, aiService: GeminiAIService, config: BotConfig) {
    this.whatsappClient = whatsappClient;
    this.aiService = aiService;
    this.config = config;
  }

  public async handleMessage(context: MessageContext): Promise<void> {
    try {
      // Log incoming message
      this.logMessage(context);

      // Check if message should be processed
      if (!this.shouldProcessMessage(context)) {
        return;
      }

      // Send typing indicator (if supported)
      console.log(`🔄 Processing message from ${context.senderName}...`);

      // Process the message with AI
      const aiResponse = await this.aiService.generateResponse(context);

      if (aiResponse.error) {
        console.error(`❌ AI Error: ${aiResponse.error}`);
      }

      // Send response back to WhatsApp
      await this.whatsappClient.sendMessage(context.from, aiResponse.text);

      console.log(`✅ Response sent to ${context.senderName}`);

    } catch (error) {
      console.error('Error in message handler:', error);
      
      // Send error message to user
      try {
        await this.whatsappClient.sendMessage(
          context.from, 
          'Maaf, terjadi kesalahan sistem. Silakan coba lagi nanti.'
        );
      } catch (sendError) {
        console.error('Failed to send error message:', sendError);
      }
    }
  }

  private shouldProcessMessage(context: MessageContext): boolean {
    const message = context.body.trim();

    // Skip empty messages
    if (!message) {
      return false;
    }

    // Skip if message is too long (to prevent abuse)
    if (message.length > 2000) {
      console.log(`⚠️ Message too long (${message.length} chars), skipping`);
      return false;
    }

    // If bot has a prefix, check if message starts with it
    if (this.config.botPrefix) {
      if (context.isGroup) {
        // In groups, only respond to messages with prefix
        return message.startsWith(this.config.botPrefix);
      } else {
        // In private chats, respond to all messages (prefix optional)
        return true;
      }
    }

    // If no prefix configured, respond to all messages
    return true;
  }

  private logMessage(context: MessageContext): void {
    const timestamp = context.timestamp.toLocaleTimeString('id-ID');
    const location = context.isGroup ? `Group: ${context.groupName}` : 'Private Chat';
    const preview = context.body.length > 50 ? 
      context.body.substring(0, 50) + '...' : 
      context.body;

    console.log(`📨 [${timestamp}] ${context.senderName} (${location}): ${preview}`);
  }

  public getProcessedMessage(context: MessageContext): string {
    let message = context.body.trim();

    // Remove prefix if present
    if (this.config.botPrefix && message.startsWith(this.config.botPrefix)) {
      message = message.substring(this.config.botPrefix.length).trim();
    }

    return message;
  }
}
