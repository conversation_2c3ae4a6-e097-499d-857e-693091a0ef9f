import * as dotenv from 'dotenv';
import { BotConfig } from '../types';

// Load environment variables
dotenv.config();

export class ConfigManager {
  private static instance: ConfigManager;
  private config: BotConfig;

  private constructor() {
    this.config = this.loadConfig();
    this.validateConfig();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private loadConfig(): BotConfig {
    return {
      botName: process.env.BOT_NAME || 'WhatsApp Gemini Bot',
      botPrefix: process.env.BOT_PREFIX || '!',
      sessionName: process.env.SESSION_NAME || 'whatsapp-session',
      geminiApiKey: process.env.GEMINI_API_KEY || '',
      serverPort: parseInt(process.env.SERVER_PORT || '3000', 10),
      enableWebInterface: process.env.ENABLE_WEB_INTERFACE !== 'false'
    };
  }

  private validateConfig(): void {
    const errors: string[] = [];

    if (!this.config.geminiApiKey) {
      errors.push('GEMINI_API_KEY is required');
    }

    if (!this.config.botName.trim()) {
      errors.push('BOT_NAME cannot be empty');
    }

    if (!this.config.sessionName.trim()) {
      errors.push('SESSION_NAME cannot be empty');
    }

    if (this.config.serverPort < 1 || this.config.serverPort > 65535) {
      errors.push('SERVER_PORT must be between 1 and 65535');
    }

    if (errors.length > 0) {
      console.error('❌ Configuration errors:');
      errors.forEach(error => console.error(`   - ${error}`));
      console.error('\n💡 Please check your .env file or environment variables.');
      console.error('📝 See .env.example for reference.');
      process.exit(1);
    }
  }

  public getConfig(): BotConfig {
    return { ...this.config };
  }

  public printConfig(): void {
    console.log('⚙️ Bot Configuration:');
    console.log(`   Bot Name: ${this.config.botName}`);
    console.log(`   Bot Prefix: ${this.config.botPrefix}`);
    console.log(`   Session Name: ${this.config.sessionName}`);
    console.log(`   Gemini API Key: ${this.config.geminiApiKey ? '✅ Configured' : '❌ Missing'}`);
    console.log(`   Server Port: ${this.config.serverPort}`);
    console.log(`   Web Interface: ${this.config.enableWebInterface ? '✅ Enabled' : '❌ Disabled'}`);
  }
}

export const getConfig = (): BotConfig => {
  return ConfigManager.getInstance().getConfig();
};

export const printConfig = (): void => {
  ConfigManager.getInstance().printConfig();
};
