"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.printConfig = exports.getConfig = exports.ConfigManager = void 0;
const dotenv = __importStar(require("dotenv"));
dotenv.config();
class ConfigManager {
    constructor() {
        this.config = this.loadConfig();
        this.validateConfig();
    }
    static getInstance() {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }
    loadConfig() {
        return {
            botName: process.env.BOT_NAME || 'WhatsApp Gemini Bot',
            botPrefix: process.env.BOT_PREFIX || '!',
            sessionName: process.env.SESSION_NAME || 'whatsapp-session',
            geminiApiKey: process.env.GEMINI_API_KEY || ''
        };
    }
    validateConfig() {
        const errors = [];
        if (!this.config.geminiApiKey) {
            errors.push('GEMINI_API_KEY is required');
        }
        if (!this.config.botName.trim()) {
            errors.push('BOT_NAME cannot be empty');
        }
        if (!this.config.sessionName.trim()) {
            errors.push('SESSION_NAME cannot be empty');
        }
        if (errors.length > 0) {
            console.error('❌ Configuration errors:');
            errors.forEach(error => console.error(`   - ${error}`));
            console.error('\n💡 Please check your .env file or environment variables.');
            console.error('📝 See .env.example for reference.');
            process.exit(1);
        }
    }
    getConfig() {
        return { ...this.config };
    }
    printConfig() {
        console.log('⚙️ Bot Configuration:');
        console.log(`   Bot Name: ${this.config.botName}`);
        console.log(`   Bot Prefix: ${this.config.botPrefix}`);
        console.log(`   Session Name: ${this.config.sessionName}`);
        console.log(`   Gemini API Key: ${this.config.geminiApiKey ? '✅ Configured' : '❌ Missing'}`);
    }
}
exports.ConfigManager = ConfigManager;
const getConfig = () => {
    return ConfigManager.getInstance().getConfig();
};
exports.getConfig = getConfig;
const printConfig = () => {
    ConfigManager.getInstance().printConfig();
};
exports.printConfig = printConfig;
//# sourceMappingURL=index.js.map