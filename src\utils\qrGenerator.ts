import * as QRCode from 'qrcode';
import * as fs from 'fs';
import * as path from 'path';

export class QRGenerator {
  static async generateQRCodeFile(
    qrData: string, 
    filePath: string, 
    options?: {
      width?: number;
      margin?: number;
      color?: {
        dark?: string;
        light?: string;
      };
    }
  ): Promise<void> {
    try {
      const defaultOptions = {
        width: 512,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        ...options
      };

      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      await QRCode.toFile(filePath, qrData, defaultOptions);
      console.log(`📱 QR Code saved to: ${path.resolve(filePath)}`);
      console.log(`💡 You can scan this QR code file manually with WhatsApp`);
    } catch (error) {
      console.error('❌ Error generating QR code file:', error);
      throw error;
    }
  }

  static async generateQRCodeDataURL(
    qrData: string, 
    options?: {
      width?: number;
      margin?: number;
      color?: {
        dark?: string;
        light?: string;
      };
    }
  ): Promise<string> {
    try {
      const defaultOptions = {
        width: 512,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        ...options
      };

      return await QRCode.toDataURL(qrData, defaultOptions);
    } catch (error) {
      console.error('❌ Error generating QR code data URL:', error);
      throw error;
    }
  }

  static deleteQRCodeFile(filePath: string): void {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`🗑️ QR Code file deleted: ${filePath}`);
      }
    } catch (error) {
      console.error('❌ Error deleting QR code file:', error);
    }
  }
}
