export interface QRCodeData {
    qrString: string;
    dataURL: string;
    timestamp: Date;
    isActive: boolean;
}
export interface WhatsAppStatus {
    isConnected: boolean;
    isAuthenticated: boolean;
    hasQRCode: boolean;
    lastUpdate: Date;
    sessionName?: string;
}
export declare class QRCodeService {
    private static instance;
    private currentQRCode;
    private whatsappStatus;
    private constructor();
    static getInstance(): QRCodeService;
    setQRCode(qrString: string): Promise<void>;
    clearQRCode(): void;
    updateWhatsAppStatus(status: Partial<WhatsAppStatus>): void;
    getCurrentQRCode(): QRCodeData | null;
    getWhatsAppStatus(): WhatsAppStatus;
    isQRCodeAvailable(): boolean;
    getQRCodeDataURL(): string | null;
    getQRCodeBuffer(): Promise<Buffer | null>;
    reset(): void;
}
//# sourceMappingURL=qrCodeService.d.ts.map