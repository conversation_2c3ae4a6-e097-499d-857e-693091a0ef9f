import { QRGenerator } from '../utils/qrGenerator';

export interface QRCodeData {
  qrString: string;
  dataURL: string;
  timestamp: Date;
  isActive: boolean;
}

export interface WhatsAppStatus {
  isConnected: boolean;
  isAuthenticated: boolean;
  hasQRCode: boolean;
  lastUpdate: Date;
  sessionName?: string;
}

export class QRCodeService {
  private static instance: QRCodeService;
  private currentQRCode: QRCodeData | null = null;
  private whatsappStatus: WhatsAppStatus = {
    isConnected: false,
    isAuthenticated: false,
    hasQRCode: false,
    lastUpdate: new Date()
  };

  private constructor() {}

  public static getInstance(): QRCodeService {
    if (!QRCodeService.instance) {
      QRCodeService.instance = new QRCodeService();
    }
    return QRCodeService.instance;
  }

  /**
   * Store a new QR code when received from WhatsApp
   */
  public async setQRCode(qrString: string): Promise<void> {
    try {
      console.log('🔄 Generating QR code for backend display...');
      
      const dataURL = await QRGenerator.generateQRCodeDataURL(qrString, {
        width: 512,
        margin: 2
      });

      this.currentQRCode = {
        qrString,
        dataURL,
        timestamp: new Date(),
        isActive: true
      };

      this.whatsappStatus = {
        ...this.whatsappStatus,
        hasQRCode: true,
        isConnected: false,
        isAuthenticated: false,
        lastUpdate: new Date()
      };

      console.log('✅ QR code stored for backend access');
    } catch (error) {
      console.error('❌ Error storing QR code:', error);
      throw error;
    }
  }

  /**
   * Clear QR code when WhatsApp is authenticated
   */
  public clearQRCode(): void {
    this.currentQRCode = null;
    this.whatsappStatus = {
      ...this.whatsappStatus,
      hasQRCode: false,
      lastUpdate: new Date()
    };
    console.log('🗑️ QR code cleared - WhatsApp authenticated');
  }

  /**
   * Update WhatsApp connection status
   */
  public updateWhatsAppStatus(status: Partial<WhatsAppStatus>): void {
    this.whatsappStatus = {
      ...this.whatsappStatus,
      ...status,
      lastUpdate: new Date()
    };
    console.log('📱 WhatsApp status updated:', this.whatsappStatus);
  }

  /**
   * Get current QR code data
   */
  public getCurrentQRCode(): QRCodeData | null {
    return this.currentQRCode;
  }

  /**
   * Get current WhatsApp status
   */
  public getWhatsAppStatus(): WhatsAppStatus {
    return { ...this.whatsappStatus };
  }

  /**
   * Check if QR code is available and not expired
   */
  public isQRCodeAvailable(): boolean {
    if (!this.currentQRCode) {
      return false;
    }

    // QR codes typically expire after 20 seconds, but we'll keep them for 30 seconds
    const expirationTime = 30 * 1000; // 30 seconds
    const now = new Date().getTime();
    const qrTime = this.currentQRCode.timestamp.getTime();

    return (now - qrTime) < expirationTime && this.currentQRCode.isActive;
  }

  /**
   * Get QR code as data URL for API responses
   */
  public getQRCodeDataURL(): string | null {
    if (!this.isQRCodeAvailable()) {
      return null;
    }
    return this.currentQRCode!.dataURL;
  }

  /**
   * Get QR code as buffer for image responses
   */
  public async getQRCodeBuffer(): Promise<Buffer | null> {
    const dataURL = this.getQRCodeDataURL();
    if (!dataURL) {
      return null;
    }

    try {
      // Convert data URL to buffer
      const base64Data = dataURL.replace(/^data:image\/png;base64,/, '');
      return Buffer.from(base64Data, 'base64');
    } catch (error) {
      console.error('❌ Error converting QR code to buffer:', error);
      return null;
    }
  }

  /**
   * Reset the service (useful for testing or restart)
   */
  public reset(): void {
    this.currentQRCode = null;
    this.whatsappStatus = {
      isConnected: false,
      isAuthenticated: false,
      hasQRCode: false,
      lastUpdate: new Date()
    };
    console.log('🔄 QR Code service reset');
  }
}
