{"name": "whatsapp-gemini-bot", "version": "1.0.0", "description": "WhatsApp bot powered by Google Gemini AI", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["whatsapp", "bot", "gemini", "ai", "typescript"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@google/genai": "^1.7.0", "dotenv": "^17.0.0", "mime": "^4.0.7", "qrcode-terminal": "^0.12.0", "whatsapp-web.js": "^1.31.0"}, "devDependencies": {"@types/node": "^24.0.7", "@types/qrcode-terminal": "^0.12.2", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}