export interface BotConfig {
    botName: string;
    botPrefix: string;
    sessionName: string;
    geminiApiKey: string;
    serverPort: number;
    enableWebInterface: boolean;
}
export interface MessageContext {
    from: string;
    body: string;
    isGroup: boolean;
    groupName?: string;
    senderName: string;
    timestamp: Date;
}
export interface AIResponse {
    text: string;
    error?: string;
}
//# sourceMappingURL=index.d.ts.map