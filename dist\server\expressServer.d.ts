import { Application } from 'express';
export declare class ExpressServer {
    private app;
    private server;
    private port;
    private qrCodeService;
    constructor(port?: number);
    private setupMiddleware;
    private setupRoutes;
    private getWebInterface;
    start(): Promise<void>;
    stop(): Promise<void>;
    getPort(): number;
    getApp(): Application;
}
//# sourceMappingURL=expressServer.d.ts.map