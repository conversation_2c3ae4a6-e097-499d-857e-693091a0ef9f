"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppClient = void 0;
const whatsapp_web_js_1 = require("whatsapp-web.js");
const qrcode = __importStar(require("qrcode-terminal"));
const qrCodeService_1 = require("../services/qrCodeService");
class WhatsAppClient {
    constructor(config) {
        this.config = config;
        this.qrCodeService = qrCodeService_1.QRCodeService.getInstance();
        this.client = new whatsapp_web_js_1.Client({
            authStrategy: new whatsapp_web_js_1.LocalAuth({
                clientId: config.sessionName
            }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ]
            }
        });
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        this.client.on('qr', async (qr) => {
            console.log('🔗 Scan the QR code below to connect WhatsApp:');
            qrcode.generate(qr, { small: true });
            try {
                await this.qrCodeService.setQRCode(qr);
                console.log('🌐 QR code is now available at the web interface');
            }
            catch (error) {
                console.error('❌ Failed to store QR code for backend:', error);
            }
        });
        this.client.on('ready', () => {
            console.log('✅ WhatsApp client is ready!');
            console.log(`🤖 Bot Name: ${this.config.botName}`);
            console.log(`📱 Session: ${this.config.sessionName}`);
            this.qrCodeService.updateWhatsAppStatus({
                isConnected: true,
                isAuthenticated: true,
                sessionName: this.config.sessionName
            });
            this.qrCodeService.clearQRCode();
        });
        this.client.on('authenticated', () => {
            console.log('🔐 WhatsApp client authenticated successfully');
            this.qrCodeService.updateWhatsAppStatus({
                isAuthenticated: true
            });
        });
        this.client.on('auth_failure', (msg) => {
            console.error('❌ Authentication failed:', msg);
            this.qrCodeService.updateWhatsAppStatus({
                isConnected: false,
                isAuthenticated: false
            });
        });
        this.client.on('disconnected', (reason) => {
            console.log('📱 WhatsApp client disconnected:', reason);
            this.qrCodeService.updateWhatsAppStatus({
                isConnected: false,
                isAuthenticated: false
            });
            this.qrCodeService.clearQRCode();
        });
    }
    onMessage(handler) {
        this.client.on('message', async (message) => {
            try {
                if (message.from === 'status@broadcast')
                    return;
                if (message.fromMe)
                    return;
                const contact = await message.getContact();
                const chat = await message.getChat();
                const context = {
                    from: message.from,
                    body: message.body,
                    isGroup: chat.isGroup,
                    groupName: chat.isGroup ? chat.name : undefined,
                    senderName: contact.pushname || contact.name || 'Unknown',
                    timestamp: new Date(message.timestamp * 1000)
                };
                await handler(context);
            }
            catch (error) {
                console.error('Error handling message:', error);
            }
        });
    }
    async sendMessage(to, message) {
        try {
            await this.client.sendMessage(to, message);
        }
        catch (error) {
            console.error('Error sending message:', error);
            throw error;
        }
    }
    async initialize() {
        try {
            console.log('🚀 Initializing WhatsApp client...');
            await this.client.initialize();
        }
        catch (error) {
            console.error('Failed to initialize WhatsApp client:', error);
            throw error;
        }
    }
    async destroy() {
        try {
            await this.client.destroy();
            console.log('🛑 WhatsApp client destroyed');
        }
        catch (error) {
            console.error('Error destroying client:', error);
        }
    }
}
exports.WhatsAppClient = WhatsAppClient;
//# sourceMappingURL=client.js.map