"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QRCodeService = void 0;
const qrGenerator_1 = require("../utils/qrGenerator");
class QRCodeService {
    constructor() {
        this.currentQRCode = null;
        this.whatsappStatus = {
            isConnected: false,
            isAuthenticated: false,
            hasQRCode: false,
            lastUpdate: new Date()
        };
    }
    static getInstance() {
        if (!QRCodeService.instance) {
            QRCodeService.instance = new QRCodeService();
        }
        return QRCodeService.instance;
    }
    async setQRCode(qrString) {
        try {
            console.log('🔄 Generating QR code for backend display...');
            const dataURL = await qrGenerator_1.QRGenerator.generateQRCodeDataURL(qrString, {
                width: 512,
                margin: 2
            });
            this.currentQRCode = {
                qrString,
                dataURL,
                timestamp: new Date(),
                isActive: true
            };
            this.whatsappStatus = {
                ...this.whatsappStatus,
                hasQRCode: true,
                isConnected: false,
                isAuthenticated: false,
                lastUpdate: new Date()
            };
            console.log('✅ QR code stored for backend access');
        }
        catch (error) {
            console.error('❌ Error storing QR code:', error);
            throw error;
        }
    }
    clearQRCode() {
        this.currentQRCode = null;
        this.whatsappStatus = {
            ...this.whatsappStatus,
            hasQRCode: false,
            lastUpdate: new Date()
        };
        console.log('🗑️ QR code cleared - WhatsApp authenticated');
    }
    updateWhatsAppStatus(status) {
        this.whatsappStatus = {
            ...this.whatsappStatus,
            ...status,
            lastUpdate: new Date()
        };
        console.log('📱 WhatsApp status updated:', this.whatsappStatus);
    }
    getCurrentQRCode() {
        return this.currentQRCode;
    }
    getWhatsAppStatus() {
        return { ...this.whatsappStatus };
    }
    isQRCodeAvailable() {
        if (!this.currentQRCode) {
            return false;
        }
        const expirationTime = 30 * 1000;
        const now = new Date().getTime();
        const qrTime = this.currentQRCode.timestamp.getTime();
        return (now - qrTime) < expirationTime && this.currentQRCode.isActive;
    }
    getQRCodeDataURL() {
        if (!this.isQRCodeAvailable()) {
            return null;
        }
        return this.currentQRCode.dataURL;
    }
    async getQRCodeBuffer() {
        const dataURL = this.getQRCodeDataURL();
        if (!dataURL) {
            return null;
        }
        try {
            const base64Data = dataURL.replace(/^data:image\/png;base64,/, '');
            return Buffer.from(base64Data, 'base64');
        }
        catch (error) {
            console.error('❌ Error converting QR code to buffer:', error);
            return null;
        }
    }
    reset() {
        this.currentQRCode = null;
        this.whatsappStatus = {
            isConnected: false,
            isAuthenticated: false,
            hasQRCode: false,
            lastUpdate: new Date()
        };
        console.log('🔄 QR Code service reset');
    }
}
exports.QRCodeService = QRCodeService;
//# sourceMappingURL=qrCodeService.js.map