import { BotConfig, MessageContext } from '../types';
export declare class WhatsAppClient {
    private client;
    private config;
    constructor(config: BotConfig);
    private setupEventHandlers;
    onMessage(handler: (context: MessageContext) => Promise<void>): void;
    sendMessage(to: string, message: string): Promise<void>;
    initialize(): Promise<void>;
    destroy(): Promise<void>;
}
//# sourceMappingURL=client.d.ts.map