import { Client, LocalAuth, Message } from 'whatsapp-web.js';
import * as qrcode from 'qrcode-terminal';
import { BotConfig, MessageContext } from '../types';
import { QRCodeService } from '../services/qrCodeService';

export class WhatsAppClient {
  private client: Client;
  private config: BotConfig;
  private qrCodeService: QRCodeService;

  constructor(config: BotConfig) {
    this.config = config;
    this.qrCodeService = QRCodeService.getInstance();
    this.client = new Client({
      authStrategy: new LocalAuth({
        clientId: config.sessionName
      }),
      puppeteer: {
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ]
      }
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.client.on('qr', async (qr: string) => {
      console.log('🔗 Scan the QR code below to connect WhatsApp:');
      qrcode.generate(qr, { small: true });

      // Store QR code for backend access
      try {
        await this.qrCodeService.setQRCode(qr);
        console.log('🌐 QR code is now available at the web interface');
      } catch (error) {
        console.error('❌ Failed to store QR code for backend:', error);
      }
    });

    this.client.on('ready', () => {
      console.log('✅ WhatsApp client is ready!');
      console.log(`🤖 Bot Name: ${this.config.botName}`);
      console.log(`📱 Session: ${this.config.sessionName}`);

      // Update status - connected and authenticated
      this.qrCodeService.updateWhatsAppStatus({
        isConnected: true,
        isAuthenticated: true,
        sessionName: this.config.sessionName
      });

      // Clear QR code since we're now authenticated
      this.qrCodeService.clearQRCode();
    });

    this.client.on('authenticated', () => {
      console.log('🔐 WhatsApp client authenticated successfully');

      // Update authentication status
      this.qrCodeService.updateWhatsAppStatus({
        isAuthenticated: true
      });
    });

    this.client.on('auth_failure', (msg: string) => {
      console.error('❌ Authentication failed:', msg);

      // Update status to reflect auth failure
      this.qrCodeService.updateWhatsAppStatus({
        isConnected: false,
        isAuthenticated: false
      });
    });

    this.client.on('disconnected', (reason: string) => {
      console.log('📱 WhatsApp client disconnected:', reason);

      // Update status to reflect disconnection
      this.qrCodeService.updateWhatsAppStatus({
        isConnected: false,
        isAuthenticated: false
      });

      // Clear QR code on disconnect
      this.qrCodeService.clearQRCode();
    });
  }

  public onMessage(handler: (context: MessageContext) => Promise<void>): void {
    this.client.on('message', async (message: Message) => {
      try {
        // Skip messages from status broadcast
        if (message.from === 'status@broadcast') return;

        // Skip messages sent by the bot itself
        if (message.fromMe) return;

        const contact = await message.getContact();
        const chat = await message.getChat();

        const context: MessageContext = {
          from: message.from,
          body: message.body,
          isGroup: chat.isGroup,
          groupName: chat.isGroup ? chat.name : undefined,
          senderName: contact.pushname || contact.name || 'Unknown',
          timestamp: new Date(message.timestamp * 1000)
        };

        await handler(context);
      } catch (error) {
        console.error('Error handling message:', error);
      }
    });
  }

  public async sendMessage(to: string, message: string): Promise<void> {
    try {
      await this.client.sendMessage(to, message);
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  public async initialize(): Promise<void> {
    try {
      console.log('🚀 Initializing WhatsApp client...');
      await this.client.initialize();
    } catch (error) {
      console.error('Failed to initialize WhatsApp client:', error);
      throw error;
    }
  }

  public async destroy(): Promise<void> {
    try {
      await this.client.destroy();
      console.log('🛑 WhatsApp client destroyed');
    } catch (error) {
      console.error('Error destroying client:', error);
    }
  }
}
