import { Client, LocalAuth, Message } from 'whatsapp-web.js';
import * as qrcode from 'qrcode-terminal';
import { BotConfig, MessageContext } from '../types';

export class WhatsAppClient {
  private client: Client;
  private config: BotConfig;

  constructor(config: BotConfig) {
    this.config = config;
    this.client = new Client({
      authStrategy: new LocalAuth({
        clientId: config.sessionName
      }),
      puppeteer: {
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ]
      }
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.client.on('qr', (qr: string) => {
      console.log('🔗 Scan the QR code below to connect WhatsApp:');
      qrcode.generate(qr, { small: true });
    });

    this.client.on('ready', () => {
      console.log('✅ WhatsApp client is ready!');
      console.log(`🤖 Bot Name: ${this.config.botName}`);
      console.log(`📱 Session: ${this.config.sessionName}`);
    });

    this.client.on('authenticated', () => {
      console.log('🔐 WhatsApp client authenticated successfully');
    });

    this.client.on('auth_failure', (msg: string) => {
      console.error('❌ Authentication failed:', msg);
    });

    this.client.on('disconnected', (reason: string) => {
      console.log('📱 WhatsApp client disconnected:', reason);
    });
  }

  public onMessage(handler: (context: MessageContext) => Promise<void>): void {
    this.client.on('message', async (message: Message) => {
      try {
        // Skip messages from status broadcast
        if (message.from === 'status@broadcast') return;

        // Skip messages sent by the bot itself
        if (message.fromMe) return;

        const contact = await message.getContact();
        const chat = await message.getChat();

        const context: MessageContext = {
          from: message.from,
          body: message.body,
          isGroup: chat.isGroup,
          groupName: chat.isGroup ? chat.name : undefined,
          senderName: contact.pushname || contact.name || 'Unknown',
          timestamp: new Date(message.timestamp * 1000)
        };

        await handler(context);
      } catch (error) {
        console.error('Error handling message:', error);
      }
    });
  }

  public async sendMessage(to: string, message: string): Promise<void> {
    try {
      await this.client.sendMessage(to, message);
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  public async initialize(): Promise<void> {
    try {
      console.log('🚀 Initializing WhatsApp client...');
      await this.client.initialize();
    } catch (error) {
      console.error('Failed to initialize WhatsApp client:', error);
      throw error;
    }
  }

  public async destroy(): Promise<void> {
    try {
      await this.client.destroy();
      console.log('🛑 WhatsApp client destroyed');
    } catch (error) {
      console.error('Error destroying client:', error);
    }
  }
}
