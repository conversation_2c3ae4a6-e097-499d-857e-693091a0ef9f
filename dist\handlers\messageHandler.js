"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageHandler = void 0;
class MessageHandler {
    constructor(whatsappClient, aiService, config) {
        this.whatsappClient = whatsappClient;
        this.aiService = aiService;
        this.config = config;
    }
    async handleMessage(context) {
        try {
            this.logMessage(context);
            if (!this.shouldProcessMessage(context)) {
                return;
            }
            console.log(`🔄 Processing message from ${context.senderName}...`);
            const aiResponse = await this.aiService.generateResponse(context);
            if (aiResponse.error) {
                console.error(`❌ AI Error: ${aiResponse.error}`);
            }
            await this.whatsappClient.sendMessage(context.from, aiResponse.text);
            console.log(`✅ Response sent to ${context.senderName}`);
        }
        catch (error) {
            console.error('Error in message handler:', error);
            try {
                await this.whatsappClient.sendMessage(context.from, '<PERSON><PERSON>, ter<PERSON><PERSON> k<PERSON><PERSON>han sistem. Silakan coba lagi nanti.');
            }
            catch (sendError) {
                console.error('Failed to send error message:', sendError);
            }
        }
    }
    shouldProcessMessage(context) {
        const message = context.body.trim();
        if (!message) {
            return false;
        }
        if (message.length > 2000) {
            console.log(`⚠️ Message too long (${message.length} chars), skipping`);
            return false;
        }
        if (this.config.botPrefix) {
            if (context.isGroup) {
                return message.startsWith(this.config.botPrefix);
            }
            else {
                return true;
            }
        }
        return true;
    }
    logMessage(context) {
        const timestamp = context.timestamp.toLocaleTimeString('id-ID');
        const location = context.isGroup ? `Group: ${context.groupName}` : 'Private Chat';
        const preview = context.body.length > 50 ?
            context.body.substring(0, 50) + '...' :
            context.body;
        console.log(`📨 [${timestamp}] ${context.senderName} (${location}): ${preview}`);
    }
    getProcessedMessage(context) {
        let message = context.body.trim();
        if (this.config.botPrefix && message.startsWith(this.config.botPrefix)) {
            message = message.substring(this.config.botPrefix.length).trim();
        }
        return message;
    }
}
exports.MessageHandler = MessageHandler;
//# sourceMappingURL=messageHandler.js.map