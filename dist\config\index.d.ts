import { BotConfig } from '../types';
export declare class ConfigManager {
    private static instance;
    private config;
    private constructor();
    static getInstance(): ConfigManager;
    private loadConfig;
    private validateConfig;
    getConfig(): BotConfig;
    printConfig(): void;
}
export declare const getConfig: () => BotConfig;
export declare const printConfig: () => void;
//# sourceMappingURL=index.d.ts.map