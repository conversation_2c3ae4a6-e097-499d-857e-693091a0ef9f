"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("./whatsapp/client");
const gemini_1 = require("./ai/gemini");
const messageHandler_1 = require("./handlers/messageHandler");
const config_1 = require("./config");
class WhatsAppGeminiBot {
    constructor() {
        const config = (0, config_1.getConfig)();
        this.whatsappClient = new client_1.WhatsAppClient(config);
        this.aiService = new gemini_1.GeminiAIService(config.geminiApiKey);
        this.messageHandler = new messageHandler_1.MessageHandler(this.whatsappClient, this.aiService, config);
    }
    async start() {
        try {
            console.log('🚀 Starting WhatsApp Gemini Bot...\n');
            (0, config_1.printConfig)();
            console.log('');
            console.log('🔍 Testing Gemini AI connection...');
            const aiConnected = await this.aiService.testConnection();
            if (!aiConnected) {
                console.error('❌ Failed to connect to Gemini AI. Please check your API key.');
                process.exit(1);
            }
            console.log('✅ Gemini AI connection successful\n');
            this.whatsappClient.onMessage(async (context) => {
                await this.messageHandler.handleMessage(context);
            });
            await this.whatsappClient.initialize();
            this.setupGracefulShutdown();
        }
        catch (error) {
            console.error('❌ Failed to start bot:', error);
            process.exit(1);
        }
    }
    setupGracefulShutdown() {
        const shutdown = async (signal) => {
            console.log(`\n🛑 Received ${signal}. Shutting down gracefully...`);
            try {
                await this.whatsappClient.destroy();
                console.log('✅ Bot shutdown complete');
                process.exit(0);
            }
            catch (error) {
                console.error('❌ Error during shutdown:', error);
                process.exit(1);
            }
        };
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('uncaughtException', (error) => {
            console.error('❌ Uncaught Exception:', error);
            shutdown('uncaughtException');
        });
        process.on('unhandledRejection', (reason, promise) => {
            console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
            shutdown('unhandledRejection');
        });
    }
}
const bot = new WhatsAppGeminiBot();
bot.start().catch((error) => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map