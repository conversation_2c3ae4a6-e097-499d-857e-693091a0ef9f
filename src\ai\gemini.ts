import { GoogleGenAI } from '@google/genai';
import { AIResponse, MessageContext } from '../types';

export class GeminiAIService {
  private ai: GoogleGenAI;
  private model: string = 'gemini-2.5-flash';

  constructor(apiKey: string) {
    this.ai = new GoogleGenAI({
      apiKey: apiKey,
    });
  }

  public async generateResponse(context: MessageContext): Promise<AIResponse> {
    try {
      const tools = [
        { urlContext: {} },
      ];

      const config = {
        thinkingConfig: {
          thinkingBudget: -1,
        },
        tools,
        responseMimeType: 'text/plain',
      };

      // Create a contextual prompt
      const systemPrompt = this.createSystemPrompt(context);
      const userMessage = context.body;

      const contents = [
        {
          role: 'user',
          parts: [
            {
              text: `${systemPrompt}\n\nUser message: ${userMessage}`,
            },
          ],
        },
      ];

      console.log(`🤖 Processing message from ${context.senderName}: ${userMessage.substring(0, 50)}...`);

      const response = await this.ai.models.generateContentStream({
        model: this.model,
        config,
        contents,
      });

      let fullResponse = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullResponse += chunk.text;
        }
      }

      if (!fullResponse.trim()) {
        return {
          text: 'Maaf, saya tidak dapat memproses pesan Anda saat ini. Silakan coba lagi.',
          error: 'Empty response from AI'
        };
      }

      console.log(`✅ Generated response (${fullResponse.length} chars)`);
      return { text: fullResponse.trim() };

    } catch (error) {
      console.error('Error generating AI response:', error);
      return {
        text: 'Maaf, terjadi kesalahan saat memproses pesan Anda. Silakan coba lagi nanti.',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private createSystemPrompt(context: MessageContext): string {
    const basePrompt = `Anda adalah asisten AI yang membantu melalui WhatsApp. 
Berikan respons yang ramah, informatif, dan sesuai konteks.
Gunakan bahasa Indonesia yang natural dan mudah dipahami.
Jika ditanya tentang hal yang tidak Anda ketahui, jujur katakan bahwa Anda tidak tahu.`;

    let contextualInfo = '';
    
    if (context.isGroup) {
      contextualInfo += `\nKonteks: Anda sedang berbicara dalam grup WhatsApp "${context.groupName}" dengan pengguna bernama ${context.senderName}.`;
    } else {
      contextualInfo += `\nKonteks: Anda sedang berbicara secara pribadi dengan ${context.senderName}.`;
    }

    contextualInfo += `\nWaktu: ${context.timestamp.toLocaleString('id-ID')}`;

    return basePrompt + contextualInfo;
  }

  public async testConnection(): Promise<boolean> {
    try {
      const testResponse = await this.ai.models.generateContentStream({
        model: this.model,
        config: {
          responseMimeType: 'text/plain',
        },
        contents: [
          {
            role: 'user',
            parts: [
              {
                text: 'Hello, please respond with "OK" if you can hear me.',
              },
            ],
          },
        ],
      });

      let response = '';
      for await (const chunk of testResponse) {
        if (chunk.text) {
          response += chunk.text;
        }
      }

      return response.trim().length > 0;
    } catch (error) {
      console.error('Gemini AI connection test failed:', error);
      return false;
    }
  }
}
